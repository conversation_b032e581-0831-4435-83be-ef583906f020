<template>
  <div>
    <el-container>
      <!--      pc-->
      <el-aside width="150px" class="web" style="position: absolute">
        <el-menu :default-active="menu" class="el-menu-vertical-demo" :default-openeds="opened" @select="handSelect">
          <el-menu-item index="1">
            <span>基础信息</span>
          </el-menu-item>

          <el-menu-item index="9">
            <span>访问记录</span>
          </el-menu-item>

          <el-menu-item index="2">
            <span>购物车列表</span>
          </el-menu-item>

          <el-menu-item index="8">
            <span>订单列表</span>
          </el-menu-item>

          <el-menu-item index="10">
            <span>结算列表</span>
          </el-menu-item>

          <el-menu-item index="7">
            <span>售后列表</span>
          </el-menu-item>

          <el-menu-item index="3">
            <span>余额账户</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!--      phone-->

      <el-header class="phone">
        <el-menu :default-active="menu" class="el-menu-vertical-demo" :default-openeds="opened" mode="horizontal"
          @select="handSelect">
          <el-menu-item index="1">
            <span>基础信息</span>
          </el-menu-item>
          <el-menu-item index="7">
            <span>售后列表</span>
          </el-menu-item>
        </el-menu>
      </el-header>

      <el-main class="main">
        <div v-if="menu === '1'">
          <buyerdetail :id="buyer_id" @getBuyerName="getBuyerName" />
        </div>

        <div v-if="menu === '2'">
          <cartList :id="buyer_id" />
        </div>
        <div v-if="menu === '3'" class="web">
          <AccountBalance :id="buyer_id" />
        </div>

        <div v-if="menu === '5'" class="web">
          <Invoice :id="buyer_id" />
        </div>

        <div v-if="menu === '7'" class="web" style="margin-left: 10px">
          <el-table :data="list" style="width: 98%">
            <el-table-column type="index" width="50" />
            <el-table-column label="封面" width="150">
              <template #default="scope">
                <div v-if="scope.row.product_cover">
                  <el-image style="width: 100px; height: 100px" preview-teleported loading="lazy" :src="baseImgUrl +
                    categoryCoverProcess +
                    scope.row.product_cover.name
                    " :preview-src-list="[
                      baseImgUrl + displayProcess + scope.row.product_cover.name
                    ]" fit="cover" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="商品名称" width="140">
              <template #default="scope">
                <div>{{ scope.row.product_title }}</div>
                <div style="font-size: 12px; color: orange">
                  规格：{{ scope.row.sku_name }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="商品图">
              <template #default="scope">
                <div v-if="scope.row.reason_img.name != ''" style="display: flex; align-items: center">
                  <el-image :preview-src-list="scope.row.reason_img.name"
                    :src="baseImgUrl + scope.row.reason_img.name" />
                </div>
              </template>
            </el-table-column>

            <el-table-column label="商品金额">
              <template #default="scope">
                <span>{{ dealMoney(scope.row.price) }}</span>
              </template>
            </el-table-column>
            <el-table-column width="140" label="申请金额">
              <template #default="scope">
                <div>
                  <div>商品： ￥{{ dealMoney(scope.row.amount) }}</div>
                  <div v-if="scope.row.total_service_fee > 0">
                    服务费： ￥{{ dealMoney(scope.row.total_service_fee) }}
                  </div>

                  <div v-if="scope.row.unit_transport_fee > 0">
                    干线费： ￥{{ dealMoney(scope.row.unit_transport_fee) }}
                  </div>
                  <div style="font-weight: bold">
                    总计： ￥{{
                      dealMoney(
                        scope.row.amount + scope.row.total_warehouse_load_fee
                      )
                    }}
                  </div>
                  <div class="">
                    金额比例:
                    {{ scope.row.applayProportion }}%
                  </div>

                  <div class="">
                    实退金额: ￥{{ dealMoney(scope.row.audit_amount) }}
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="申请重量">
              <template #default="scope">{{ dealWeight(scope.row.refund_weight) }}kg
              </template>
            </el-table-column>

            <el-table-column label="售后类型" width="140" align="center">
              <template #default="scope">
                <el-tag>{{ AfterSaleTypes(scope.row.refund_reason_type) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="supplier_name" label="供应商" />

            <el-table-column label="审核状态" width="180">
              <template #default="scope">
                <div style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                  ">
                  <span>审核状态：</span>
                  <el-tag>{{ AuditStatusMsg[scope.row.audit_status] }}</el-tag>
                </div>

                <div v-if="audit_status === 2" style="display: flex; align-items: center; margin-top: 10px">
                  <span>退款方式：</span>
                  <span v-if="
                    scope.row.refund_back_type == 'mini' ||
                    scope.row.refund_back_type == 'wechat'
                  ">
                    <el-tag>微信支付</el-tag>
                  </span>

                  <span v-if="scope.row.refund_back_type == 'coupon'">
                    <el-tag>代金券</el-tag>
                  </span>
                  <span v-if="scope.row.refund_back_type == 'balance'">
                    <el-tag>钱包余额</el-tag>
                  </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="售后理由">
              <template #default="scope">
                {{ scope.row.reason }}
              </template>
            </el-table-column>

            <el-table-column label="时间" width="230">
              <template #default="scope">
                <div>
                  <span>订单时间：{{ dealTime(scope.row.created_at) }}</span>
                </div>
                <div v-if="audit_status === 2">
                  <span>退款时间：{{ scope.row.refund_result.pay_datetime }}</span>
                </div>

                <div>
                  <span v-if="scope.row.updated_at !== 0">更新时间：{{ dealTime(scope.row.updated_at) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label>
              <template #default="scope">
                <el-button @click="refoundDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination v-model:current-page="page" v-model:page-size="limit" :page-sizes="[10, 15, 20]" :small="small"
            :background="background" layout="sizes, prev, pager, next" :total="count" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>

        <div v-if="menu === '8'">
          <orderList :id="buyer_id" :buyer_name="buyer_name" />
        </div>

        <div v-if="menu === '9'" class="web">
          <AccessRecords />
        </div>

        <div v-if="menu === '10'" class="web">
          <differences :id="buyer_id" />
        </div>

        <div v-if="menu === '7'" class="phone">
          <div>
            <div v-for="item in list" :key="item.id" class="main-list">
              <div v-if="list.length > 0" class="list">
                <div style="font-size: 14px">
                  {{ item.supplier_name }}
                  <span style="color: #595959">
                    {{ dealTime(item.created_at) }}
                  </span>
                  <span v-if="item.audit_status == 1">
                    <van-tag type="primary" style="margin-left: 5px">
                      {{ AuditStatusMsg[item.audit_status] }}
                    </van-tag>
                  </span>

                  <span v-if="item.audit_status == 2">
                    <van-tag type="success" style="margin-left: 5px">
                      {{ AuditStatusMsg[item.audit_status] }}
                    </van-tag>
                  </span>

                  <span v-if="item.audit_status == 3">
                    <van-tag type="warning" style="margin-left: 5px">
                      {{ AuditStatusMsg[item.audit_status] }}
                    </van-tag>
                  </span>
                </div>

                <div style="display: flex">
                  <div v-if="item.product_cover">
                    <van-image width="80" height="80" :src="baseImgUrl +
                      categoryCoverProcess +
                      item.product_cover.name
                      " :preview-src-list="[
                        baseImgUrl + displayProcess + item.product_cover.name
                      ]" />
                  </div>
                  <div style="
                      display: flex;
                      flex: 1;
                      flex-direction: column;
                      justify-content: space-between;
                      margin-left: 10px;
                    ">
                    <div style="font-size: 14px">
                      {{ item.product_title }}
                    </div>
                    <div style="
                        display: flex;
                        align-items: center;
                        margin-bottom: 6px;
                      ">
                      <div v-if="item.refund_type !== 1" style="padding: 0 6px; text-align: center">
                        <span style="font-size: 12px; color: #3a3a3c">仓配费</span>
                        <div v-if="item.refund_type !== 1" style="font-size: 12px">
                          {{ dealMoney(item.total_warehouse_load_fee) }}
                        </div>
                      </div>

                      <div v-if="item.refund_type !== 1" style="height: 30px; border-left: 1px solid #eef" />

                      <div style="padding: 0 6px; text-align: center">
                        <span style="font-size: 12px; color: #3a3a3c">{{
                          item.refund_type == 1 ? "申请重量" : "重量"
                        }}</span>
                        <div style="font-size: 12px">
                          {{ dealWeight(item.refund_weight) }}kg
                        </div>
                      </div>
                      <div v-if="item.refund_type == 1" style="height: 30px; border-left: 1px solid #eef" />

                      <div v-if="item.refund_type == 1" style="padding: 0 6px; color: red; text-align: center">
                        <span style="font-size: 12px; color: red">申请金额</span>
                        <div style="font-size: 12px">
                          {{ dealMoney(item.amount) }}元
                        </div>
                      </div>

                      <div v-if="item.refund_type == 1 && item.audit_status == 2"
                        style="height: 30px; border-left: 1px solid #eef" />

                      <div v-if="item.refund_type == 1 && item.audit_status == 2"
                        style="padding: 0 6px; color: red; text-align: center">
                        <span style="font-size: 12px; color: red">审核金额</span>
                        <div style="font-size: 12px">
                          {{ dealMoney(item.audit_amount) }}元
                        </div>
                      </div>

                      <div style="height: 30px; border-left: 1px solid #eef" />

                      <div style="padding: 0 6px; color: red; text-align: center">
                        <span style="font-size: 12px; color: red">金额比例</span>
                        <div style="font-size: 12px">
                          {{ item.applayProportion }}%
                        </div>
                      </div>

                      <div v-if="item.refund_type == 1 && item.audit_status !== 2"
                        style="height: 30px; border-left: 1px solid #eef" />

                      <div v-if="item.audit_status !== 2" style="padding: 0 6px; color: red; text-align: center">
                        <span style="font-size: 12px; color: red">总计</span>
                        <div style="font-size: 12px">
                          {{
                            dealMoney(
                              item.amount + item.total_warehouse_load_fee
                            )
                          }}元
                        </div>
                      </div>
                    </div>
                    <div v-if="item.refund_reason_type" style="display: flex; align-items: center">
                      <div style="margin-right: 5px; color: red">
                        ￥{{ dealMoney(item.price) }}
                      </div>

                      <el-tag>{{ AfterSaleTypes(item.refund_reason_type) }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div v-if="item.audit_status == 2" style="margin-top: 4px; font-size: 12px; color: #a1a1a1">
                  <span style="color: #3a3a3c">实退金额：</span>
                  {{ dealMoney(item.audit_amount) }}
                </div>
                <div style="margin-top: 4px; font-size: 12px; color: #a1a1a1">
                  <span style="color: #3a3a3c">申请理由：</span>

                  {{ item.reason }}
                </div>
                <div v-if="item.audit_note !== ''" style="margin-top: 4px; font-size: 12px; color: #a1a1a1">
                  <span style="color: #3a3a3c">审核备注：</span>
                  {{ item.audit_note }}
                </div>

                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 12px;
                    color: #595959;
                  ">
                  <div />
                  <van-button hairline type="primary" style="height: 25px" @click="refoundDetail(item)">详情
                  </van-button>
                </div>
              </div>
            </div>

            <van-pagination v-if="list.length > 0" v-model="page" :items-per-page="15" :show-page-size="5"
              :total-items="count" force-ellipses @change="handleCurrentChange" />

            <van-empty v-if="list.length == 0" description="无售后" />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, defineAsyncComponent } from "vue";

const buyerdetail = defineAsyncComponent(
  () => import("@/components/buyer/buyerdetail.vue")
);
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import dayjs from "dayjs";
import { afterSaleList } from "@/api/buyer";
import { useRouter } from "vue-router";
import { AfterSaleTypes, AuditStatusMsg, RefundStatusMsg } from "@/utils/dict";
import Invoice from "@/components/buyer/Invoice.vue";
import differences from "./differences.vue";
import AccessRecords from "./AccessRecords.vue";
import orderList from "./OrderList.vue";
import cartList from "./cartList.vue";
import orderExport from "./OrderExport.vue";
import AccountBalance from "./AccountBalance.vue";
import { baseImgUrl, categoryCoverProcess, displayProcess } from "@/api/utils";
import duration from "dayjs/plugin/duration";
import localeZh from "dayjs/locale/zh-cn";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.locale(localeZh);
dayjs.extend(duration);
dayjs.extend(relativeTime);
let audit_status = ref(2);
let showTime = ref(false);
const menu = ref<any>("7");
let opened = ["1"];
let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let small = ref(false);
let background = ref(false);
let list = ref([]);
let buyer_id = ref<any>("");
let buyer_name = ref("");
let user_id = ref<any>("");

onMounted(() => {
  buyer_id.value = router.currentRoute.value.query.id;
  user_id.value = router.currentRoute.value.query.user_id;
  menu.value = router.currentRoute.value.query.menu;
  if (menu.value === "7") {
    toList(page.value, limit.value);
  }
});

//菜单
const handSelect = key => {
  menu.value = key;
  page.value = 1;
  if (key == "7") {
    toList(page.value, limit.value);
  }
};

function getBuyerName(e) {
  buyer_name.value = e.toString();
}

function toList(p, l) {
  let data = {
    page: p,
    limit: l,
    buyer_id: buyer_id.value
  };

  // return;
  afterSaleList(data).then(res => {
    if (res.code === 0) {
      let state = res.data.list;
      count.value = res.data.count;
      if (!state) {
        state = [];
      }
      state.forEach(res => {
        res.applayProportion = ((res.amount / res.price) * 100).toFixed(2);
      });

      list.value = state;
    }
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (menu.value == "7") {
    toList(page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;
  if (menu.value == "7") {
    toList(page.value, limit.value);
  }
};

function refoundDetail(item) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: item.order_id,
      buyer_id: item.buyer_id
    }
  });
  window.open(routeUrl.href, "_blank");
}
</script>

<style>
@media screen and (width <=750px) {

  /* 小于750 */
  .web {
    display: none;
  }

  .phone {
    display: block;
  }

  .main {
    margin-left: 0;
  }
}

/* 大于750 */

@media screen and (width >=750px) {
  .web {
    display: block;
  }

  .phone {
    display: none;
  }

  .main {
    margin-left: 130px;
  }
}

.main-list {
  box-sizing: border-box;
  padding: 10px;
  margin: 0 10rpx 10px;
  background-color: #fff;
  border-radius: 10px;
}

.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.list {
  background-color: #fff;
  border-radius: 10px;
}

.el-main {
  padding-top: 0 !important;
}
</style>
